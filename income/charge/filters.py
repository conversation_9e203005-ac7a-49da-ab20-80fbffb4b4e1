from django.utils.encoding import force_str
from rest_framework.filters import Base<PERSON>ilterBackend

from income.contrib.drf.utils import get_value
from income.contrib.drf.utils import make_markdown_table


class IncomeChargeDetailFieldsFilter(BaseFilterBackend):
    """
    支持独立字段筛选的过滤器
    """

    def filter_queryset(self, request, queryset, view):
        """
        根据独立的字段参数进行筛选
        """

        # 需要查询的字段,需要在ViewSet中配置
        search_fields = getattr(view, "search_fields", [])
        # 是否为模糊查询
        search_contains = getattr(view, "search_contains", False)
        # 表别名映射,需要在ViewSet中配置
        table_mapping = getattr(view, "table_mapping", {})

        # 兼容 DRF Request 和 Django WSGIRequest
        query_params = getattr(request, "query_params", request.GET)
        value_mapping = {
            search_field: query_params.get(search_field, "").strip()
            for search_field in search_fields
            if search_field in query_params
        }
        # 如果没有任何筛选条件, 返回原始查询
        if not any(value_mapping.values()):
            return queryset

        # 构建筛选条件
        return self._build_filtered_queryset(
            queryset,
            search_contains,
            value_mapping,
            table_mapping,
        )

    def _build_filtered_queryset(
        self,
        queryset,
        search_contains,
        value_mapping,
        table_mapping,
    ):
        """执行筛选

        :param queryset: RawQuerySet
        :param search_contains: 是否为模糊搜索
        :param value_mapping: 筛选字段和值的映射
        :param table_mapping: 筛选字段对应的表名(简写表名或者表名皆可)
        :return:
        """
        from .models import IncomeChargeDetail

        # 构建基础SQL,删除排序
        base_sql = queryset.query.sql.replace("ORDER BY a.`created_at` DESC", "")

        # 构建WHERE条件和参数
        where_conditions = []
        params = []

        for search_field, value in value_mapping.items():
            if not value:
                continue
            if search_contains:
                where_conditions.append(
                    f"{table_mapping[search_field]}.`{search_field}` LIKE %s",
                )
                params.append(f"%{value}%")
            else:
                where_conditions.append(
                    f"{table_mapping[search_field]}.`{search_field}` = %s",
                )
                params.append(value)

        # 组装完整SQL
        if where_conditions:
            where_clause = " AND ".join(where_conditions)
            sql = f"{base_sql} WHERE {where_clause} ORDER BY a.`created_at` DESC"
            return IncomeChargeDetail.objects.raw(sql, params)

        sql = f"{base_sql} ORDER BY a.`created_at` DESC"
        return IncomeChargeDetail.objects.raw(sql)

    def get_schema_operation_parameters(self, view):
        search_fields = getattr(view, "search_fields", [])
        return [
            {
                "name": search_field,
                "required": False,
                "in": "query",
                "title": force_str(search_field),
                "description": force_str(search_field),
                "schema": {
                    "type": "string",
                },
            }
            for search_field in search_fields
        ]


class AdjustTypeFilter(BaseFilterBackend):
    """
    调账类别筛选
    type_param
    status_choices: 状态的枚举
    """

    type_param = "adjust_type"
    type_choices = "adjust_type_choices"

    def filter_queryset(self, request, queryset, view):
        type_param = get_value(request, self.type_param)
        type_choices = getattr(view, self.type_choices)
        if type_param.isnumeric():
            if int(type_param) in type_choices.values:
                queryset = queryset.filter(adjust_type=type_param)
        return queryset

    def get_schema_operation_parameters(self, view):
        type_choices = getattr(view, self.type_choices)
        return [
            {
                "name": self.type_param,
                "required": False,
                "in": "query",
                "description": force_str(make_markdown_table(type_choices.choices)),
                "schema": {
                    "type": "enum",
                    "enum": type_choices.values,
                },
            },
        ]
