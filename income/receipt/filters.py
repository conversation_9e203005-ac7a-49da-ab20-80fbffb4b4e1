from django.db import models
from django.utils.encoding import force_str
from rest_framework.filters import BaseFilterBackend

from income import const
from income.contrib.drf.utils import get_value
from income.contrib.drf.utils import make_markdown_table


class ReceiptStatusFilter(BaseFilterBackend):
    """
    对账状态筛选
    type_param
    status_choices: 状态的枚举
    """

    type_param = "receipt_status"
    type_choices = "receipt_status_choices"

    def filter_queryset(self, request, queryset, view):
        type_param = get_value(request, self.type_param)
        type_choices = getattr(view, self.type_choices)
        if type_param.isnumeric():
            if int(type_param) in type_choices.values:
                # 待认款【amount-confirmed_amount>0 && confirming_amount = 0
                # 待确认【confirming_amount>0
                # 已认款【amount-confirmed_amount=0】
                if int(type_param) == type_choices.PENDING_CLAIM:
                    queryset = queryset.filter(
                        amount__gt=models.F("confirmed_amount"),
                        confirming_amount=0,
                    )
                elif int(type_param) == type_choices.PENDING_CONFIRMATION:
                    queryset = queryset.filter(confirming_amount__gt=0)
                elif int(type_param) == type_choices.CONFIRMED:
                    queryset = queryset.filter(
                        amount__lte=models.F("confirmed_amount"),
                    )
        return queryset

    def get_schema_operation_parameters(self, view):
        type_choices = getattr(view, self.type_choices)
        return [
            {
                "name": self.type_param,
                "required": False,
                "in": "query",
                "description": force_str(make_markdown_table(type_choices.choices)),
                "schema": {
                    "type": "enum",
                    "enum": type_choices.values,
                },
            },
        ]
