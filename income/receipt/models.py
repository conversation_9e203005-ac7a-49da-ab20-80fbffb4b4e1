from django.db import models


class IncomeBankStatement(models.Model):
    """对账信息表"""

    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        db_comment="交易金额(精确到分)",
    )
    currency_type = models.CharField(
        max_length=10,
        blank=True,
        null=True,
        db_comment="币种",
    )
    statement_no = models.CharField(unique=True, max_length=50, db_comment="对账单编号")
    statement_date = models.DateField(db_comment="对账单日期")
    payment_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        db_comment="付款方名称",
    )
    payment_bank_no = models.CharField(
        max_length=30,
        blank=True,
        null=True,
        db_comment="付款方银行账号",
    )
    payment_bank_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        db_comment="付款方银行名称",
    )
    receive_bank_name = models.Char<PERSON>ield(
        max_length=40,
        blank=True,
        null=True,
        db_comment="收款银行名称",
    )
    receive_bank_no = models.Char<PERSON><PERSON>(
        max_length=50,
        blank=True,
        null=True,
        db_comment="收款方银行账号",
    )
    confirming_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        db_comment="待确认金额",
    )
    confirmed_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        db_comment="已确认金额",
    )
    customer_num = models.CharField(
        max_length=40,
        blank=True,
        null=True,
        db_comment="客户编号",
    )
    customer_name = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        db_comment="客户名称",
    )
    state = models.CharField(max_length=20, db_comment="对账状态")
    description = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        db_comment="备注",
    )
    created_at = models.DateTimeField(blank=True, null=True, db_comment="创建时间")
    updated_at = models.DateTimeField(blank=True, null=True, db_comment="更新时间")

    class Meta:
        managed = False
        db_table = "income_bank_statement"
        db_table_comment = "对账信息表"
        ordering = ("-created_at",)

    def __str__(self):
        return f"<IncomeBankStatement: {self.statement_no}>"
