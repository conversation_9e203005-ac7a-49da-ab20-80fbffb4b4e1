from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import extend_schema_view
from rest_framework import mixins

from income import const
from income.contrib.drf.filters import SearchAndFilter
from income.contrib.drf.views import GenericViewSet
from income.contrib.drf.views import UpdateModelMixin
from income.permissions import IsAuthenticated
from income.permissions import RoleMenuPermission

from .filters import ReceiptStatusFilter
from .models import IncomeBankStatement
from .serializers import IncomeBankStatementSerializer


@extend_schema_view(
    list=extend_schema(summary="获取对账信息"),
    update=extend_schema(summary="更新对账信息"),
)
@extend_schema(tags=["bank-statement"])
class IncomeBankStatementViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
    UpdateModelMixin,
):
    serializer_class = IncomeBankStatementSerializer
    permission_classes = [IsAuthenticated, RoleMenuPermission]
    filter_backends = [SearchAndFilter, ReceiptStatusFilter]
    search_fields = ["statement_no", "payment_name"]
    search_contains = True

    receipt_status_choices = const.ReceiptStatus

    identify = const.MenuIdentify.INCOME_RECEIPT

    def get_queryset(self):
        return IncomeBankStatement.objects.all()
