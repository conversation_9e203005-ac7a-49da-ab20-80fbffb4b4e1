from collections import defaultdict

from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import extend_schema_view
from rest_framework import mixins
from rest_framework.response import Response

from income import const
from income.contrib.drf.filters import SearchSingleFieldFilter
from income.contrib.drf.views import GenericViewSet, UpdateModelMixin
from income.permissions import IsAuthenticated
from income.users.models import User, UserDepartment
from income.users.permissions import DepartmentBelowPermission
from income.users.serializers import DepartmentSerializer
from income.users.serializers import DepartmentCreateSerializer
from income.users.serializers import DepartmentUpdateSerializer
from income.users.serializers import UsersSerializer
from income.users.serializers import UserUpdateSerializer
from income.users.utils import add_extra_field


@extend_schema_view(
    list=extend_schema(summary="获取用户信息"),
)
class UsersViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
    mixins.CreateModelMixin,
    UpdateModelMixin
):
    search_single_field = "username"
    filter_backends = [SearchSingleFieldFilter]
    serializer_class = UsersSerializer
    serializers = {
        "update": UserUpdateSerializer,
    }
    permission_classes = [IsAuthenticated, DepartmentBelowPermission]

    identify = const.MenuIdentify.USER_INFO

    def get_queryset(self):
        return User.objects.filter(department_id=self.kwargs["department_id"])

    def get_serializer_class(self):
        return self.serializers.get(self.action, self.serializer_class)

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset()).values(
            "id",
            "username",
            "email",
            "state",
            "department_id",
            "mobile",
        )
        page = self.paginate_queryset(queryset)
        if page is not None:
            data = add_extra_field(page)
            return self.get_paginated_response(data)
        data = add_extra_field(queryset)
        return Response(data)


@extend_schema_view(
    list=extend_schema(summary="获取部门信息"),
)
class UserDepartmentViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
    mixins.CreateModelMixin,
    UpdateModelMixin,
):
    permission_classes = [IsAuthenticated]
    pagination_class = None

    serializer_class = DepartmentSerializer
    serializers = {
        "create": DepartmentCreateSerializer,
        "update": DepartmentUpdateSerializer,
    }

    identify = const.MenuIdentify.USER_DEPARTMENT

    def get_queryset(self):
        return UserDepartment.objects.all()

    def get_serializer_class(self):
        return self.serializers.get(self.action, self.serializer_class)

    def list(self, request, *args, **kwargs):

        fields = ["id", "department_name", "parent_id"]
        department_info_list = self.filter_queryset(self.get_queryset()).values(*fields)

        departments = []
        parent_map = defaultdict(list)  # 父级id对应的子级列表
        for department_info in department_info_list:
            parent_id = department_info["parent_id"]
            data_dict = {
                "id": department_info["id"],
                "department_name": department_info["department_name"],
            }
            if parent_id is None:
                departments.append(data_dict)
            else:
                parent_map[parent_id].append(data_dict)

        def get_child_data(info):
            child_data_list = parent_map.get(info["id"])
            info["children"] = child_data_list or None
            if child_data_list:
                for child_data in child_data_list:
                    get_child_data(child_data)

        for department in departments:
            get_child_data(department)

        return Response(departments)
