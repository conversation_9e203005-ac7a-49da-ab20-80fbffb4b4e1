from collections import defaultdict

from django.contrib import auth
from drf_spectacular.utils import extend_schema
from rest_framework import status
from rest_framework.decorators import api_view
from rest_framework.decorators import authentication_classes
from rest_framework.decorators import permission_classes
from rest_framework.response import Response

from income import message
from income.permissions import IsAuthenticated
from income.users.models import User  # noqa: TC001
from income.users.serializers import LoginSerializer
from income.users.serializers import LogoutSerializer
from income.users.serializers import ProfileSerializer
from income.users.utils import get_profile_menu_info
from income.utils.menu_operation import create_role_permission


@extend_schema(
    tags=["users"],
    summary="用户登录",
    request=LoginSerializer,
    responses={
        status.HTTP_200_OK: str,
        status.HTTP_422_UNPROCESSABLE_ENTITY: str,
    },
)
@api_view(["POST"])
@authentication_classes([])
@permission_classes([])
def login(request):
    serializer = LoginSerializer(
        data=request.data,
        context={"request": request},
    )
    serializer.is_valid(raise_exception=True)
    user = auth.authenticate(
        request,
        username=serializer.data["username"],
        password=serializer.data["password"],
    )
    if user is None:
        return Response(
            data={"message": message.USERNAME_OR_PASSWORD_ERROR},
            status=status.HTTP_422_UNPROCESSABLE_ENTITY,
        )
    auth.login(request, user)
    # 用户登录成功后, 如果用户不是超级管理员, 在redis中插入用户的访问和操作权限
    if not user.is_admin:
        create_role_permission(user.user_roles, user.id)
    return Response(data=message.LOGIN_SUCCESS, status=200)


@extend_schema(
    tags=["users"],
    summary="用户登出",
    request=LogoutSerializer,
    responses={status.HTTP_200_OK: str, },
)
@api_view(["POST"])
@permission_classes([IsAuthenticated])
def logout(request):
    auth.logout(request)
    return Response(
        data=message.LOGOUT_SUCCESS,
        status=status.HTTP_200_OK,
    )


@extend_schema(
    tags=["users"],
    responses={200: ProfileSerializer},
    summary="获取当前用户的信息",
)
@api_view(["GET"])
@permission_classes([IsAuthenticated])
def profile(request, *args, **kwargs):
    user: User = request.user

    menu_info_list = get_profile_menu_info(
        role_ids=user.user_roles, super_admin=user.is_admin,
    )
    menus = []
    parent_map = defaultdict(list)  # 父级id对应的子级列表
    for menu_info in menu_info_list:
        parent_id = menu_info["parent_id"]
        data_dict = {
            "id": menu_info["menu_id"],
            "name": menu_info["menu_name"],
            "identify": menu_info["identify"],
        }
        menu_info["icon"] and data_dict.update(icon=menu_info["icon"])
        menu_info["router"] and data_dict.update(router=menu_info["router"])
        menu_info["operation"] and data_dict.update(operation=menu_info["operation"])
        if parent_id is None:
            menus.append(data_dict)
        else:
            parent_map[parent_id].append(data_dict)

    def get_child_data(info):
        child_data_list = parent_map.get(info["id"])
        info["children"] = child_data_list or None
        if child_data_list:
            for child_data in child_data_list:
                get_child_data(child_data)

    for menu in menus:
        get_child_data(menu)

    result = {
        "username": user.username,
        "email": user.email,
        "menus": menus,
    }
    return Response(result)
