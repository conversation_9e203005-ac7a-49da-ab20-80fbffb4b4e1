from django.db import transaction
from rest_framework import serializers
from django_redis import get_redis_connection

from income import message, const
from income.users.models import (
    Menu,
    Role,
    RoleMenu,
    UserRole, User,
)
from income.utils.menu_operation import create_role_permission


class RoleMenuSerializer(serializers.ModelSerializer):
    operation = serializers.BooleanField(
        help_text="是否可以操作", default=True, allow_null=True
    )
    children = serializers.ListField(
        help_text="子菜单列表",
        default=[{
            "id": 0,
            "menu_name": "子菜单",
            "icon": "pi pi-file",
            "operation": True,
            "children": None
        }],
        child=serializers.DictField(),
    )

    class Meta:
        model = Menu
        fields = (
            "id",
            "menu_name",
            "icon",
            "operation",
            "children"
        )


class RoleSerializer(serializers.ModelSerializer):
    menus = serializers.ListSerializer(
        help_text="菜单列表", child=RoleMenuSerializer(), source="get_menus"
    )

    class Meta:
        model = Role
        fields = ["id", "role_name", "role_code", "menus"]


class RoleSimpleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Role
        fields = ("id", "role_name")


class RoleMenuCreateSerializer(serializers.Serializer):
    menu_id = serializers.IntegerField(help_text="菜单ID", required=True)
    operation = serializers.BooleanField(
        help_text="是否可操作",
        allow_null=True,
        required=True,
    )


class RoleCreateSerializer(serializers.ModelSerializer):
    menus = serializers.ListSerializer(
        help_text="角色拥有的菜单",
        child=RoleMenuCreateSerializer(),
        write_only=True,
    )
    serializers.CharField()

    class Meta:
        model = Role
        fields = ("role_name", "role_code", "menus")

    def validate_menus(self, value):
        menu_ids = [i.get("menu_id") for i in value]
        all_menu_id = Menu.objects.all().values_list("id", flat=True)
        # 判断menu_ids是否存在不存在的
        if not set(menu_ids).issubset(set(all_menu_id)):
            raise serializers.ValidationError(message.MENU_NOT_FOUND)
        return value

    def validate_role_code(self, value):
        if value == const.SUPER_ADMIN:
            raise serializers.ValidationError(message.ROLE_CODE_ERROR)
        return value

    def create(self, validated_data):
        menus = validated_data.pop("menus")
        # 创建角色
        instance = self.Meta.model.objects.create(**validated_data)
        # 创建RoleMenu关系
        with transaction.atomic():
            RoleMenu.objects.bulk_create(
                [
                    RoleMenu(
                        menu_id=menu.get("menu_id"),
                        operation=menu.get("operation"),
                        role_id=instance.id,
                    )
                    for menu in menus
                ],
                batch_size=50
            )
        return instance

    @staticmethod
    def update_user_permissions(role_id):

        redis_client = get_redis_connection()
        # 获取角色对应的所有用户
        user_ids = UserRole.objects.filter(role_id=role_id).values_list("user_id", flat=True)
        queryset = User.objects.filter(id__in=user_ids)
        mapping = {
            i.id: i.user_roles
            for i in queryset
            if redis_client.exists(f"identify:user-{i.id}")
        }
        for k, v in mapping.items():
            create_role_permission(v, k)

    def update(self, instance, validated_data):
        menus = validated_data.pop("menus")
        for k, v in validated_data.items():
            setattr(instance, k, v)
        instance.save()
        # 删除之前的角色对应的菜单信息
        RoleMenu.objects.filter(role_id=instance.id).delete()
        # 角色对应的新菜单入库
        RoleMenu.objects.bulk_create(
            [
                RoleMenu(
                    menu_id=menu.get("menu_id"),
                    operation=menu.get("operation"),
                    role_id=instance.id,
                )
                for menu in menus
            ],
            batch_size=50
        )
        # 将赋有该角色的用户的操作权限进行变动
        self.update_user_permissions(instance.id)
        return instance
