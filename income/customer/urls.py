from django.urls import include
from django.urls import path
from rest_framework.routers import SimpleRouter

from .views import ContactInfoViewSet
from .views import CustomersInfoApproveViewSet
from .views import CustomersInfoViewSet

router = SimpleRouter(trailing_slash=False)
router.register("customers-info", CustomersInfoViewSet, basename="customers-info")

contact_router = SimpleRouter(trailing_slash=False)
contact_router.register("contacts-info", ContactInfoViewSet, basename="contacts-info")

approve_router = SimpleRouter(trailing_slash=False)
approve_router.register(
    "customers-approve", CustomersInfoApproveViewSet, basename="customers-approve",
)

urlpatterns = [
    path("", include(router.urls)),
    path("customers/<int:customer_id>/", include(contact_router.urls)),
    path("", include(approve_router.urls)),
]
