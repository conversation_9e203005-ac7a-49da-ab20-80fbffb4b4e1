from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import extend_schema_view
from rest_framework import mixins
from rest_framework.decorators import action
from rest_framework.response import Response

from income import const
from income import message
from income.contrib.drf.filters import SearchFilter
from income.contrib.drf.views import GenericViewSet
from income.contrib.drf.views import UpdateModelMixin
from income.customer.filters import ApproveStateFilter
from income.customer.models import ContactInfo
from income.customer.models import CustomerApproveHistory
from income.customer.models import CustomerInfo
from income.customer.permissions import CustomerPermission
from income.customer.serializers import ContactInfoSerializer
from income.customer.serializers import CustomerApproveHistorySerializer
from income.customer.serializers import CustomerApproveSerializer
from income.customer.serializers import CustomerInfoSimpleSerializer
from income.customer.serializers import CustomersInfoApproveSerializer
from income.customer.serializers import CustomersInfoSerializer
from income.permissions import IsAuthenticated
from income.permissions import RoleMenuPermission


@extend_schema_view(
    list=extend_schema(
        summary="获取客户信息列表",
        description="获取客户信息列表",
    ),
    retrieve=extend_schema(
        summary="获取客户信息详情",
        description="获取客户信息详情",
    ),
    create=extend_schema(
        summary="创建客户信息",
        description="创建客户信息",
    ),
    update=extend_schema(
        summary="更新客户信息",
        description="更新客户信息",
    ),
)
class CustomersInfoViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
    mixins.CreateModelMixin,
    mixins.RetrieveModelMixin,
    UpdateModelMixin,
):
    """客户信息"""

    NOT_FOUND_MESSAGE = message.CUSTOMER_NOT_FOUND

    serializer_class = CustomersInfoSerializer
    serializers = {
        "simple_list": CustomerInfoSimpleSerializer,
        "approve": CustomerApproveSerializer,
        "approve_history": CustomerApproveHistorySerializer,
    }
    permission_classes = [IsAuthenticated, RoleMenuPermission]

    filter_backends = [SearchFilter, ApproveStateFilter]
    search_fields = ["customer_name", "create_user", "sale_name"]
    search_contains = True
    approve_state_choices = const.ApproveState

    identify = const.MenuIdentify.CUSTOMER_INFO

    def get_queryset(self):
        return CustomerInfo.objects.all()

    def get_serializer_class(self):
        return self.serializers.get(self.action, self.serializer_class)

    @extend_schema(
        tags=["customers-info"],
        responses={200: CustomerInfoSimpleSerializer(many=True)},
        summary="仅展示客户信息ID、客户名称、客户编号",
    )
    @action(
        methods=["get"],
        detail=False,
        url_path="simple-list",
        filter_backends=[],
        pagination_class=None,
    )
    def simple_list(self, request, *args, **kwargs):
        queryset = self.get_queryset().values(
            "id",
            "customer_name",
            "customer_num",
        )
        return Response(queryset)

    @extend_schema(
        tags=["customers-info"],
        request=CustomerApproveSerializer,
        summary="客户信息审批流程",
        description="处理客户信息的审批流程,包括提交审核、撤回、审批通过和驳回操作",
    )
    @action(
        methods=["post"],
        detail=False,
        url_path="approve",
    )
    def approve(self, request, *args, **kwargs):
        """客户信息审批流程

        1. 只有init状态可提交审核,提交后状态变更为confirm
        2. 当状态是confirm时可进行撤回操作,撤回后状态变更为revoke
        3. 当状态是confirm时可进行审批操作
           审批通过后状态变更为active,审批驳回后状态变更为back
        """
        serializer = CustomerApproveSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        customer = serializer.validated_data["customer_instance"]
        action = serializer.validated_data["action"]
        reason = serializer.validated_data.get("reason", "")

        # 记录操作前的状态
        from_state = customer.approve_state

        # 根据不同的操作更新状态
        customer.approve_state = const.APPROVE_MAP[action]
        customer.approve_user = request.user.username

        # 记录操作后的状态
        to_state = customer.approve_state

        # 更新数据库
        update_fields = ["approve_state", "updated_at"]
        if action in ["approve", "reject"]:
            update_fields.append("approve_user")

        customer.save(update_fields=update_fields)

        # 记录审批历史
        CustomerApproveHistory.objects.create(
            customer_id=customer.id,
            action=action,
            from_state=from_state,
            to_state=to_state,
            reason=reason,
            operator=request.user.username,
        )
        return Response()

    @extend_schema(
        tags=["customers-info"],
        responses={200: CustomerApproveHistorySerializer(many=True)},
        summary="获取客户审批历史记录",
    )
    @action(
        methods=["get"],
        detail=True,
        url_path="approve-history",
        filter_backends=[],
    )
    def approve_history(self, request, pk=None):
        """获取客户审批历史记录"""
        # 检查客户是否存在
        customer = self.get_object()

        # 获取该客户的审批历史记录
        queryset = CustomerApproveHistory.objects.filter(customer_id=customer.id)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)


@extend_schema_view(
    list=extend_schema(
        summary="获取客户联系人信息列表",
        responses={200: ContactInfoSerializer(many=True)},
    ),
    create=extend_schema(summary="创建客户联系人信息"),
    update=extend_schema(summary="更新ID对应的客户联系人信息"),
    retrieve=extend_schema(summary="获取单个客户联系人详细信息"),
    destroy=extend_schema(summary="删除客户联系人信息"),
)
@extend_schema(tags=["contact-info"])
class ContactInfoViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
    mixins.CreateModelMixin,
    UpdateModelMixin,
):
    """客户联系人信息"""

    NOT_FOUND_MESSAGE = message.CONTACT_NOT_FOUND

    pagination_class = None
    serializer_class = ContactInfoSerializer
    permission_classes = [IsAuthenticated, CustomerPermission, RoleMenuPermission]

    identify = const.MenuIdentify.CUSTOMER_INFO

    def get_queryset(self):
        return ContactInfo.objects.filter(customer_id=self.kwargs["customer_id"])

    def perform_create(self, serializer):
        serializer.save(customer_id=self.kwargs["customer_id"])


@extend_schema_view(
    list=extend_schema(
        summary="获取客户信息列表",
        description="获取客户信息列表",
    ),
)
class CustomersInfoApproveViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
):
    """客户可审批信息"""

    serializer_class = CustomersInfoApproveSerializer
    permission_classes = [IsAuthenticated, RoleMenuPermission]

    identify = const.MenuIdentify.CUSTOMER_APPROVE

    def get_queryset(self):
        return CustomerInfo.objects.filter(approve_state=const.ApproveState.CONFIRM)
