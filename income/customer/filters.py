from django.utils.encoding import force_str
from rest_framework.filters import Base<PERSON>ilterBackend

from income.contrib.drf.utils import get_value
from income.contrib.drf.utils import make_markdown_table


class ApproveStateFilter(BaseFilterBackend):
    """审批状态过滤器

    state_param
    state_choices: 审批状态的枚举
    """

    state_param = "approve_state"
    state_choices = "approve_state_choices"

    def filter_queryset(self, request, queryset, view):
        approve_state = get_value(request, self.state_param)
        state_choices = getattr(view, self.state_choices)
        if approve_state:
            if approve_state in state_choices.values:
                queryset = queryset.filter(approve_state=approve_state)
        return queryset

    def get_schema_operation_parameters(self, view):
        state_choices = getattr(view, self.state_choices)
        return [
            {
                "name": self.state_param,
                "required": False,
                "in": "query",
                "description": force_str(make_markdown_table(state_choices.choices)),
                "schema": {
                    "type": "enum",
                    "enum": state_choices.values,
                },
            },
        ]
