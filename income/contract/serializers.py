from datetime import datetime

from django.db.models import Max
from rest_framework import serializers

from income import const
from income import message
from income.customer.models import CustomerInfo

from .models import ContractInfo
from .models import IncomeInvoiceInfo


class ContractSerializer(serializers.ModelSerializer):

    class Meta:
        model = ContractInfo
        exclude = ("updated_at",)
        read_only_fields = ("create_user", "group_approve_state")

    def validate_main_customer_num(self, value):
        """校验客户编码是否存在"""
        if self.instance and self.instance.main_customer_num == value:
            return value

        if not CustomerInfo.objects.filter(customer_num=value).exists():
            raise serializers.ValidationError(message.CUSTOMER_NOT_FOUND)
        return value

    def create(self, validated_data):
        create_user = self.context["request"].user.username
        validated_data["create_user"] = create_user
        return self.Meta.model.objects.create(**validated_data)


class ContractSimpleSerializer(serializers.ModelSerializer):
    customer_id = serializers.IntegerField(read_only=True)

    class Meta:
        model = ContractInfo
        fields = (
            "id",
            "contract_title",
            "contract_num",
            "customer_id",
        )


class InvoiceInfoSerializer(serializers.ModelSerializer):

    class Meta:
        model = IncomeInvoiceInfo
        exclude = ("updated_at",)
        read_only_fields = (
            "create_user",
            "account_seq",
            "sub_account_seq",
            "status",
        )

    def validate_contract_num(self, value):
        """校验合同信息是否存在"""

        if self.instance and self.instance.contract_num == value:
            return value

        # TODO: 合同信息表增加了审批状态,需要选择已审批过的合同
        if not ContractInfo.objects.filter(contract_num=value).exists():
            raise serializers.ValidationError(message.CONTRACT_NOT_FOUND)
        return value

    @staticmethod
    def generate_account_seq():
        current_time = datetime.now().strftime("%Y%m")  # noqa: DTZ005
        max_account_seq = IncomeInvoiceInfo.objects.filter(
            account_seq__startswith=f"FZ-{current_time}",
        ).aggregate(number_max=Max("account_seq"))
        max_num = max_account_seq.get("number_max")
        return f"FZ-{current_time}{int(max_num[9:]) + 1:0>4}" if max_num else f"{current_time}0001"  # noqa: E501

    @staticmethod
    def generate_sub_account_seq(account_seq: str):
        """根据当前的分账序号生成分账子序号"""

        max_sub_account_seq = IncomeInvoiceInfo.objects.filter(
            account_seq=account_seq,
        ).aggregate(number_max=Max("sub_account_seq"))
        max_num = max_sub_account_seq.get("number_max")
        return f"{int(max_num) + 1:0:6}" if max_num else "000001"

    def validate(self, attrs):
        """Validate that charge_start_day is not greater than charge_end_day."""

        charge_start_day = attrs.get("charge_start_day")
        charge_end_day = attrs.get("charge_end_day")
        if (
            charge_start_day
            and charge_end_day
            and charge_start_day > charge_end_day
        ):
            raise serializers.ValidationError(
                {"charge_start_day": message.INVALID_DATE_RANGE},
            )
        # 如果为编辑操作时,不需要重新生成account_seq和sub_account_seq
        if self.instance:
            return attrs
        account_seq = self.generate_account_seq()
        attrs["account_seq"] = account_seq
        attrs["sub_account_seq"] = self.generate_sub_account_seq(account_seq)
        return attrs

    def create(self, validated_data):
        create_user = self.context["request"].user.username
        validated_data["create_user"] = create_user
        validated_data["status"] = const.AccountSeqStatus.DRAFT
        return self.Meta.model.objects.create(**validated_data)

    def update(self, instance, validated_data):
        request = self.context["request"]
        validated_data["create_user"] = request.user.username
        for k, v in validated_data.items():
            setattr(instance, k, v)
        instance.save()
        return instance


class InvoiceInfoSimpleSerializer(serializers.ModelSerializer):

    class Meta:
        model = IncomeInvoiceInfo
        fields = ("id", "customer_invoice_name", "account_seq")
