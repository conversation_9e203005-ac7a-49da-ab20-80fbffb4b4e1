from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import extend_schema_view
from rest_framework import mixins
from rest_framework.decorators import action
from rest_framework.response import Response

from income import const
from income import message
from income.contrib.drf.filters import SearchFilter
from income.contrib.drf.views import GenericViewSet
from income.contrib.drf.views import UpdateModelMixin
from income.permissions import IsAuthenticated
from income.permissions import RoleMenuPermission

from .filters import ContractNumberFilter
from .filters import CustomerNumberFilter
from .models import ContractInfo
from .models import IncomeInvoiceInfo
from .serializers import ContractSerializer
from .serializers import ContractSimpleSerializer
from .serializers import InvoiceInfoSerializer
from .serializers import InvoiceInfoSimpleSerializer
from .utils import add_extra_field
from .utils import invoice_info_add_extra_field


@extend_schema_view(
    list=extend_schema(summary="获取合同信息"),
    create=extend_schema(summary="新建合同信息"),
    update=extend_schema(summary="编辑合同信息"),
)
@extend_schema(tags=["customers-contract"])
class ContractInfoViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
    mixins.CreateModelMixin,
    UpdateModelMixin,
):
    NOT_FOUND_MESSAGE = message.CONTRACT_NOT_FOUND

    serializer_class = ContractSerializer
    permission_classes = (IsAuthenticated,)

    search_fields = [
        "contract_title",
        "contract_num",
        "create_user",
        "main_customer_num",
    ]
    search_contains = True
    filter_backends = (SearchFilter, CustomerNumberFilter)

    identify = const.MenuIdentify.CONTRACT_INFO

    def get_queryset(self):
        return ContractInfo.objects.all()

    @extend_schema(
        tags=["customers-contract"],
        responses={200: ContractSimpleSerializer(many=True)},
        summary="仅展示合同信息ID、合同标题、合同编号、客户ID",
    )
    @action(
        methods=["get"],
        detail=False,
        url_path="simple-list",
        filter_backends=[],
        pagination_class=None,
    )
    def simple_list(self, request, *args, **kwargs):
        queryset = self.get_queryset().values(
            "id",
            "contract_num",
            "contract_title",
            "main_customer_num",
        )
        data = add_extra_field(list(queryset))
        return Response(data)


@extend_schema_view(
    list=extend_schema(summary="获取发票信息"),
    create=extend_schema(summary="新建发票信息"),
    update=extend_schema(summary="编辑发票信息"),
)
@extend_schema(tags=["invoice-info"])
class InvoiceInfoViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
    mixins.CreateModelMixin,
    UpdateModelMixin,
    mixins.RetrieveModelMixin,
):
    NOT_FOUND_MESSAGE = message.INVOICE_INFO_NOT_FOUND

    serializer_class = InvoiceInfoSerializer
    permission_classes = (IsAuthenticated, RoleMenuPermission)

    search_fields = [
        "customer_invoice_name",
        "create_user",
    ]
    filter_backends = (SearchFilter, ContractNumberFilter)
    search_contains = True

    identify = const.MenuIdentify.INVOICE_INFO

    def get_queryset(self):
        return IncomeInvoiceInfo.objects.all()

    def list(self, request, *args, **kwargs):
        fields = [field.name for field in IncomeInvoiceInfo._meta.fields]  # noqa: SLF001
        queryset = self.filter_queryset(
            self.get_queryset(),
        ).values(*fields)
        page = self.paginate_queryset(queryset)
        if page is not None:
            data = invoice_info_add_extra_field(page)
            return self.get_paginated_response(data)
        data = invoice_info_add_extra_field(queryset)
        return Response(data)


    @extend_schema(
        tags=["invoice-info"],
        responses={200: InvoiceInfoSimpleSerializer(many=True)},
        summary="仅展示发票ID、发票信息名称、分账序号",
    )
    @action(
        methods=["get"],
        detail=False,
        url_path="simple-list",
        filter_backends=[ContractNumberFilter],
        pagination_class=None,
    )
    def simple_list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset()).filter(
            status=const.AccountSeqStatus.APPROVED,
        ).values(
            "id", "customer_invoice_name", "account_seq",
        )
        return Response(queryset)
