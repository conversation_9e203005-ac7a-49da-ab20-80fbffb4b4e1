from income.contract.models import ContractInfo
from income.customer.models import CustomerInfo


def add_extra_field(query_data_list: list):
    """合同信息查询时获取所属客户的ID

    :param query_data_list: CustomerInfo Queryset数据
    :return:
    """
    customer_num_list = [
        query_data["main_customer_num"] for query_data in query_data_list
    ]

    customer_qs = CustomerInfo.objects.filter(
        customer_num__in=set(customer_num_list)
    ).values_list("customer_num", "id")
    customer_map = dict(customer_qs)

    for query_data in query_data_list:
        query_data["customer_id"] = customer_map.get(query_data.pop("main_customer_num"))
    return query_data_list


def invoice_info_add_extra_field(query_data_list: list):

    contract_num_list = [
       query_data["contract_num"] for query_data in query_data_list
    ]

    contract_qs = ContractInfo.objects.filter(
        contract_num__in=set(contract_num_list)
    ).values_list("contract_num", "contract_title")
    contract_map = dict(contract_qs)
    for query_data in query_data_list:
        query_data["contract_title"] = contract_map.get(query_data.pop("contract_num"))
        query_data["created_at"] = query_data["created_at"].strftime("%s")
    return query_data_list
