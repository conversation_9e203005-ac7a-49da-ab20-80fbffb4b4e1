from django.urls import include
from django.urls import path
from rest_framework.routers import SimpleRouter

from .views import ContractInfoViewSet
from .views import InvoiceInfoViewSet

router = SimpleRouter(trailing_slash=False)
router.register("contracts", ContractInfoViewSet, basename="contract_info")

invoice_info_router = SimpleRouter(trailing_slash=False)
invoice_info_router.register("invoices-info", InvoiceInfoViewSet, basename="invoice_info")

urlpatterns = [
    path("customers/", include(router.urls)),
    path("contract/", include(invoice_info_router.urls)),
]
